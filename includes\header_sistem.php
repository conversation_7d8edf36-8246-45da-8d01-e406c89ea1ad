<?php
/**
 * Header Sistem
 * Sistem Tempahan Bilik Mesyuarat
 */

if (!defined('NAMA_SISTEM')) {
    require_once __DIR__ . '/../config/sistem_config.php';
}

// Muatkan fungsi-fungsi peranan pengguna
require_once __DIR__ . '/user_roles.php';

$halaman_semasa = basename($_SERVER['PHP_SELF']);
?>

<!DOCTYPE html>
<html lang="ms">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= isset($tajuk_halaman) ? $tajuk_halaman . ' - ' : '' ?><?= NAMA_SISTEM ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- CSS Sistem Bersih -->
    <link href="<?= URL_SISTEM ?>/css/sistem_bersih.css" rel="stylesheet">
    <!-- Custom CSS -->
    <style>
        :root {
            --primary-color: #667eea;
            --secondary-color: #764ba2;
            --success-color: #28a745;
            --danger-color: #dc3545;
            --warning-color: #ffc107;
            --info-color: #17a2b8;
        }
        
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
        }
        
        .navbar-custom {
            background: linear-gradient(135deg, var(--primary-color) 30%, var(--secondary-color) 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .nav-link {
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .nav-link:hover {
            transform: translateY(-2px);
        }
        
        .dropdown-menu {
            border: none;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .main-content {
            min-height: calc(100vh - 200px);
            padding-top: 2rem;
            padding-bottom: 2rem;
        }
        
        .card {
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 20px rgba(0,0,0,0.15);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            border: none;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .alert {
            border: none;
            border-radius: 10px;
        }
        
        .badge {
            font-size: 0.75rem;
        }
        
        .notification-badge {
            position: absolute;
            top: -5px;
            right: -5px;
            background: var(--danger-color);
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 0.7rem;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .admin-navbar {
            border-bottom: 3px solid #dc3545;
        }

        .coordinator-navbar {
            border-bottom: 3px solid #ffc107;
        }

        .head-navbar {
            border-bottom: 3px solid #28a745;
        }
    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="navbar navbar-expand-lg navbar-dark navbar-custom <?php
        if (isset($_SESSION['profile_id'])) {
            switch ($_SESSION['profile_id']) {
                case 2: echo 'admin-navbar'; break;
                case 3: echo 'coordinator-navbar'; break;
                case 4: echo 'head-navbar'; break;
            }
        }
    ?>">
        <div class="container">
            <a class="navbar-brand" href="<?= URL_SISTEM ?>/Menuutama/dashboard.php">
                <i class="bi bi-door-open me-2"></i><?= NAMA_SISTEM ?>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <?php if (semakLogin()): ?>
                    <!-- Menu untuk pengguna yang sudah log masuk -->
                    <ul class="navbar-nav me-auto">
                        <li class="nav-item">
                            <a class="nav-link <?= ($halaman_semasa == 'dashboard.php') ? 'active' : '' ?>"
                               href="<?= URL_SISTEM ?>/Menuutama/dashboard.php">
                                <i class="bi bi-house me-1"></i>Dashboard
                            </a>
                        </li>
                        
                        <li class="nav-item">
                            <a class="nav-link <?= ($halaman_semasa == 'senarai_bilik.php') ? 'active' : '' ?>"
                               href="<?= URL_SISTEM ?>/Menuutama/senarai_bilik.php">
                                <i class="bi bi-door-open me-1"></i>Bilik Mesyuarat
                            </a>
                        </li>
                        
                        <li class="nav-item">
                            <a class="nav-link <?= ($halaman_semasa == 'tempah_bilik.php') ? 'active' : '' ?>"
                               href="<?= URL_SISTEM ?>/BuatTempahan/tempah_bilik.php">
                                <i class="bi bi-calendar-plus me-1"></i>Tempah Bilik
                            </a>
                        </li>
                        
                        <li class="nav-item">
                            <a class="nav-link <?= ($halaman_semasa == 'senarai_tempahan.php') ? 'active' : '' ?>"
                               href="<?= URL_SISTEM ?>/Menuutama/senarai_tempahan.php">
                                <i class="bi bi-calendar-check me-1"></i>Senarai Tempahan
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link <?= ($halaman_semasa == 'kalendar.php') ? 'active' : '' ?>"
                               href="<?= URL_SISTEM ?>/Menuutama/kalendar.php">
                                <i class="bi bi-calendar3 me-1"></i>Kalendar
                            </a>
                        </li>
                        
                        <?php if (semakPeranan('pentadbir')): ?>
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                    <i class="bi bi-gear me-1"></i>Pentadbiran
                                </a>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="<?= URL_SISTEM ?>/admin/dashboard.php">
                                        <i class="bi bi-speedometer2 me-2"></i>Dashboard Admin
                                    </a></li>
                                    <li><a class="dropdown-item" href="<?= URL_SISTEM ?>/admin/pengurusan_pengguna.php">
                                        <i class="bi bi-people me-2"></i>Pengurusan Pengguna
                                    </a></li>
                                    <li><a class="dropdown-item" href="<?= URL_SISTEM ?>/admin/rooms.php">
                                        <i class="bi bi-door-closed me-2"></i>Pengurusan Bilik
                                    </a></li>
                                    <li><a class="dropdown-item" href="<?= URL_SISTEM ?>/BuatTempahan/kelulusan_tempahan.php">
                                        <i class="bi bi-calendar-event me-2"></i>Pengurusan Tempahan
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="<?= URL_SISTEM ?>/Menuutama/kalendar.php">
                                        <i class="bi bi-graph-up me-2"></i>Kalendar
                                    </a></li>
                                    <li><a class="dropdown-item" href="<?= URL_SISTEM ?>/Menuutama/profil.php">
                                        <i class="bi bi-sliders me-2"></i>Tetapan Sistem
                                    </a></li>
                                </ul>
                            </li>
                        <?php endif; ?>
                    </ul>
                    
                    <!-- Menu pengguna -->
                    <ul class="navbar-nav">
                        <!-- Notifikasi -->
                        <li class="nav-item dropdown">
                            <a class="nav-link position-relative" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="bi bi-bell"></i>
                                <?php
                                // Dapatkan bilangan notifikasi yang belum dibaca
                                try {
                                    $sql_notifikasi = "SELECT COUNT(*) as bilangan FROM notifikasi 
                                                      WHERE pengguna_id = ? AND dibaca = FALSE";
                                    $result_notifikasi = $db->fetch($sql_notifikasi, [$_SESSION['pengguna_id']]);
                                    $bilangan_notifikasi = $result_notifikasi['bilangan'] ?? 0;
                                    
                                    if ($bilangan_notifikasi > 0):
                                ?>
                                    <span class="notification-badge"><?= $bilangan_notifikasi ?></span>
                                <?php endif; ?>
                                <?php } catch (Exception $e) { /* Abaikan ralat */ } ?>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end" style="width: 300px;">
                                <li><h6 class="dropdown-header">Notifikasi</h6></li>
                                <?php
                                try {
                                    $sql_notifikasi_list = "SELECT * FROM notifikasi 
                                                           WHERE pengguna_id = ? 
                                                           ORDER BY tarikh_cipta DESC LIMIT 5";
                                    $notifikasi_list = $db->fetchAll($sql_notifikasi_list, [$_SESSION['pengguna_id']]);
                                    
                                    if (empty($notifikasi_list)):
                                ?>
                                    <li><span class="dropdown-item-text text-muted">Tiada notifikasi</span></li>
                                <?php else: ?>
                                    <?php foreach ($notifikasi_list as $notifikasi): ?>
                                        <li>
                                            <a class="dropdown-item <?= $notifikasi['dibaca'] ? '' : 'fw-bold' ?>" href="#">
                                                <div class="d-flex">
                                                    <div class="flex-grow-1">
                                                        <div class="fw-semibold"><?= htmlspecialchars($notifikasi['tajuk']) ?></div>
                                                        <div class="small text-muted"><?= htmlspecialchars(substr($notifikasi['mesej'], 0, 50)) ?>...</div>
                                                        <div class="small text-muted"><?= formatTarikhMasa($notifikasi['tarikh_cipta']) ?></div>
                                                    </div>
                                                </div>
                                            </a>
                                        </li>
                                    <?php endforeach; ?>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item text-center" href="<?= URL_SISTEM ?>/notifikasi.php">Lihat Semua</a></li>
                                <?php endif; ?>
                                <?php } catch (Exception $e) { ?>
                                    <li><span class="dropdown-item-text text-muted">Ralat memuatkan notifikasi</span></li>
                                <?php } ?>
                            </ul>
                        </li>
                        
                        <!-- Profil pengguna -->
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <?php
                                // Dapatkan maklumat peranan pengguna
                                $peranan_info = getPerananInfo($_SESSION['profile_id'] ?? 1);
                                $peranan_display = $peranan_info['display'];
                                $peranan_icon = $peranan_info['icon'];
                                $peranan_badge = $peranan_info['badge'];
                                ?>
                                <i class="<?= $peranan_icon ?> me-1"></i><?= htmlspecialchars($_SESSION['nama_penuh']) ?>
                                <small class="d-block text-light opacity-75">
                                    <?= htmlspecialchars($peranan_display) ?>
                                </small>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li class="dropdown-header">
                                    <strong><?= htmlspecialchars($_SESSION['nama_penuh']) ?></strong><br>
                                    <small class="text-muted">
                                        No. KP: <?= htmlspecialchars($_SESSION['nokp']) ?><br>
                                        Peranan: <span class="badge <?= $peranan_badge ?>"><i class="<?= $peranan_icon ?> me-1"></i><?= htmlspecialchars($peranan_display) ?></span><br>
                                        <?php if (!empty($_SESSION['jawatan']) || !empty($_SESSION['gred'])): ?>
                                            Jawatan: <?= htmlspecialchars($_SESSION['jawatan'] ?? '') ?><?= !empty($_SESSION['gred']) ? ' (' . htmlspecialchars($_SESSION['gred']) . ')' : '' ?><br>
                                        <?php endif; ?>
                                        <?php if (!empty($_SESSION['bahagian'])): ?>
                                            Bahagian: <?= htmlspecialchars($_SESSION['bahagian']) ?><?= !empty($_SESSION['unit']) ? ' - ' . htmlspecialchars($_SESSION['unit']) : '' ?>
                                        <?php endif; ?>
                                    </small>
                                </li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="<?= URL_SISTEM ?>/Menuutama/profil.php">
                                    <i class="bi bi-person me-2"></i>Profil Saya
                                </a></li>
                                <?php if (isset($_SESSION['profile_id']) && $_SESSION['profile_id'] == 2 || $_SESSION['profile_id'] == 3): ?>
                                <li><a class="dropdown-item" href="<?= URL_SISTEM ?>/admin/kawalan.php">
                                    <i class="bi bi-gear-fill me-2"></i>Kawalan
                                </a></li>
                                <?php endif; ?>
                                <?php if (isset($_SESSION['profile_id']) && $_SESSION['profile_id'] == 3): ?>
                                <li><a class="dropdown-item" href="<?= URL_SISTEM ?>/penyelaras/senarai_tempahan_penyelaras.php">
                                    <i class="bi bi-person-badge me-2"></i>Penyelaras
                                </a></li>
                                <?php endif; ?>
                                <?php if (isset($_SESSION['profile_id']) && $_SESSION['profile_id'] == 2): ?>
                                <li><a class="dropdown-item" href="<?= URL_SISTEM ?>/admin/pengurusan_pengguna.php">
                                    <i class="bi bi-people-fill me-2"></i>Senarai Pengguna
                                </a></li>
                                <?php endif; ?>
                                <li><a class="dropdown-item" href="<?= URL_SISTEM ?>/Logdaftar/reset_password.php">
                                    <i class="bi bi-key me-2"></i>Tukar Kata Laluan
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="<?= URL_SISTEM ?>/Logdaftar/log_keluar.php">
                                    <i class="bi bi-box-arrow-right me-2"></i>Log Keluar
                                </a></li>
                            </ul>
                        </li>
                    </ul>
                <?php else: ?>
                    <!-- Menu untuk tetamu -->
                    <ul class="navbar-nav ms-auto">
                        <li class="nav-item">
                            <a class="nav-link" href="<?= URL_SISTEM ?>/Logdaftar/log_masuk.php">
                                <i class="bi bi-box-arrow-in-right me-1"></i>Log Masuk
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?= URL_SISTEM ?>/Logdaftar/daftar.php">
                                <i class="bi bi-person-plus me-1"></i>Daftar
                            </a>
                        </li>
                    </ul>
                <?php endif; ?>
            </div>
        </div>
    </nav>
    
    <!-- Main Content Container -->
    <div class="main-content">
        <div class="container">
            <?php
            // Papar mesej flash jika ada
            $mesej_flash = dapatkanMesejFlash();
            if ($mesej_flash):
                echo paparMesej($mesej_flash['jenis'], $mesej_flash['mesej']);
            endif;
            ?>


